<!DOCTYPE html>
<html>
<head>
    <title>Generate Product Placeholders</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .placeholder { margin: 10px; display: inline-block; }
        canvas { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Product Image Placeholders</h1>
    <p>Right-click on each image and "Save image as..." to save to the images folder.</p>
    
    <div id="placeholders"></div>

    <script>
        const products = [
            { name: "Need For Speed Polo", filename: "polo1.jpg", color: "#ff4181" },
            { name: "Urban Rebel Hoodie", filename: "hoodie1.jpg", color: "#8e24aa" },
            { name: "Street King Tee", filename: "tee1.jpg", color: "#00bcd4" },
            { name: "Neon Dreams Jacket", filename: "jacket1.jpg", color: "#4caf50" },
            { name: "Cyber Punk Shorts", filename: "shorts1.jpg", color: "#ff9800" },
            { name: "Galaxy Vibes Sweatshirt", filename: "sweatshirt1.jpg", color: "#9c27b0" }
        ];

        function createPlaceholder(product) {
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 400;
            const ctx = canvas.getContext('2d');

            // Gradient background
            const gradient = ctx.createLinearGradient(0, 0, 400, 400);
            gradient.addColorStop(0, product.color);
            gradient.addColorStop(1, '#1a1a2e');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 400);

            // Product name
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(product.name, 200, 200);

            // TrueGenZ brand
            ctx.font = 'bold 16px Arial';
            ctx.fillText('TrueGenZ', 200, 230);

            // Convert to downloadable image
            const link = document.createElement('a');
            link.download = product.filename;
            link.href = canvas.toDataURL();
            
            const div = document.createElement('div');
            div.className = 'placeholder';
            div.innerHTML = `<h3>${product.name}</h3>`;
            div.appendChild(canvas);
            div.appendChild(document.createElement('br'));
            div.appendChild(link);
            link.textContent = `Download ${product.filename}`;
            
            return div;
        }

        const container = document.getElementById('placeholders');
        products.forEach(product => {
            container.appendChild(createPlaceholder(product));
        });
    </script>
</body>
</html>
