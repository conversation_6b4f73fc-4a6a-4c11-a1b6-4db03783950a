<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrueGenZ Admin - Stock Management Dashboard</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts - Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            font-family: 'Poppins', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .neon-accent {
            border-color: #ff4181;
            box-shadow: 0 0 10px rgba(255, 65, 129, 0.3);
        }
        
        .neon-accent:focus {
            border-color: #8e24aa;
            box-shadow: 0 0 20px rgba(142, 36, 170, 0.5);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #ff4181, #8e24aa);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #8e24aa, #ff4181);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 65, 129, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .table-row {
            transition: all 0.3s ease;
        }
        
        .table-row:hover {
            background: rgba(255, 65, 129, 0.1);
            transform: translateX(5px);
        }
        
        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 10;
            backdrop-filter: blur(10px);
        }
        
        .fade-in {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite;
        }
        
        @keyframes pulseGlow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(255, 65, 129, 0.3);
            }
            50% {
                box-shadow: 0 0 30px rgba(255, 65, 129, 0.6), 0 0 60px rgba(142, 36, 170, 0.4);
            }
        }
        
        .quantity-btn {
            transition: all 0.2s ease;
        }
        
        .quantity-btn:hover {
            transform: scale(1.1);
        }
        
        .quantity-btn:active {
            transform: scale(0.95);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen text-white">
    <!-- Header -->
    <header class="glassmorphism p-6 mb-8">
        <div class="container mx-auto flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">
                    TrueGenZ Admin
                </h1>
                <p class="text-gray-300">Stock Management Dashboard</p>
            </div>
            <div class="flex items-center space-x-4">
                <a href="index.html" class="btn-secondary text-white px-4 py-2 rounded-lg hover:scale-105 transition-all duration-300">
                    <i class="fas fa-home mr-2"></i>Back to Store
                </a>
                <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Add Product Form -->
    <div class="container mx-auto px-6 mb-8">
        <div class="glassmorphism rounded-2xl p-8 pulse-glow">
            <h2 class="text-2xl font-bold mb-6 text-center">Add New Product</h2>
            <form id="addProductForm" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                    <label class="block text-sm font-medium mb-2">Product Name</label>
                    <input type="text" id="productName" class="w-full p-3 bg-transparent border border-gray-600 rounded-lg neon-accent text-white placeholder-gray-400" placeholder="Enter product name" required>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Size</label>
                    <select id="productSize" class="w-full p-3 bg-transparent border border-gray-600 rounded-lg neon-accent text-white" required>
                        <option value="">Select Size</option>
                        <option value="S">S</option>
                        <option value="M">M</option>
                        <option value="L">L</option>
                        <option value="XL">XL</option>
                        <option value="XXL">XXL</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Quantity</label>
                    <input type="number" id="productQuantity" class="w-full p-3 bg-transparent border border-gray-600 rounded-lg neon-accent text-white placeholder-gray-400" placeholder="Enter quantity" min="0" required>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-2">Product Image</label>
                    <input type="file" id="productImage" class="w-full p-3 bg-transparent border border-gray-600 rounded-lg neon-accent text-white" accept="image/*">
                </div>
                <div class="md:col-span-2 lg:col-span-4">
                    <button type="submit" class="btn-primary w-full text-white py-3 rounded-lg font-semibold hover:scale-105 transition-all duration-300">
                        <i class="fas fa-plus mr-2"></i>Add Product
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Products Table -->
    <div class="container mx-auto px-6">
        <div class="glassmorphism rounded-2xl overflow-hidden">
            <div class="sticky-header bg-gradient-to-r from-pink-600 to-purple-600 p-4">
                <h2 class="text-2xl font-bold text-center">Product Inventory</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="sticky-header bg-gray-800 bg-opacity-50">
                        <tr>
                            <th class="p-4 text-left font-semibold">Product Name</th>
                            <th class="p-4 text-left font-semibold">Size</th>
                            <th class="p-4 text-left font-semibold">Quantity</th>
                            <th class="p-4 text-left font-semibold">Actions</th>
                            <th class="p-4 text-left font-semibold">Image</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- Products will be dynamically loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Success/Error Messages -->
    <div id="messageContainer" class="fixed top-6 right-6 z-50 space-y-3"></div>

    <script>
        // Sample initial products data
        let products = [
            { id: 1, name: "Need For Speed Polo", size: "M", quantity: 25, image: "images/polo1.jpg" },
            { id: 2, name: "Need For Speed Polo", size: "L", quantity: 30, image: "images/polo1.jpg" },
            { id: 3, name: "Urban Rebel Hoodie", size: "S", quantity: 15, image: "images/hoodie1.jpg" },
            { id: 4, name: "Urban Rebel Hoodie", size: "M", quantity: 20, image: "images/hoodie1.jpg" },
            { id: 5, name: "Street King Tee", size: "L", quantity: 40, image: "images/tee1.jpg" },
            { id: 6, name: "Neon Dreams Jacket", size: "XL", quantity: 12, image: "images/jacket1.jpg" },
            { id: 7, name: "Cyber Punk Shorts", size: "M", quantity: 35, image: "images/shorts1.jpg" },
            { id: 8, name: "Galaxy Vibes Sweatshirt", size: "L", quantity: 18, image: "images/sweatshirt1.jpg" }
        ];
        
        let nextId = 9;
        
        // Load products into table
        function loadProducts() {
            const tbody = document.getElementById('productsTableBody');
            tbody.innerHTML = '';
            
            products.forEach((product, index) => {
                const row = document.createElement('tr');
                row.className = 'table-row border-b border-gray-700 opacity-0';
                row.innerHTML = `
                    <td class="p-4 font-medium">${product.name}</td>
                    <td class="p-4">
                        <span class="bg-gradient-to-r from-pink-500 to-purple-500 px-3 py-1 rounded-full text-sm font-semibold">
                            ${product.size}
                        </span>
                    </td>
                    <td class="p-4">
                        <span class="text-2xl font-bold ${product.quantity < 10 ? 'text-red-400' : product.quantity < 20 ? 'text-yellow-400' : 'text-green-400'}">
                            ${product.quantity}
                        </span>
                    </td>
                    <td class="p-4">
                        <div class="flex space-x-2">
                            <button onclick="updateQuantity(${product.id}, -1)" class="quantity-btn bg-red-600 hover:bg-red-700 text-white w-8 h-8 rounded-full flex items-center justify-center">
                                <i class="fas fa-minus text-sm"></i>
                            </button>
                            <button onclick="updateQuantity(${product.id}, 1)" class="quantity-btn bg-green-600 hover:bg-green-700 text-white w-8 h-8 rounded-full flex items-center justify-center">
                                <i class="fas fa-plus text-sm"></i>
                            </button>
                            <button onclick="deleteProduct(${product.id})" class="quantity-btn bg-gray-600 hover:bg-gray-700 text-white w-8 h-8 rounded-full flex items-center justify-center">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        </div>
                    </td>
                    <td class="p-4">
                        <img src="${product.image}" alt="${product.name}" class="w-12 h-12 object-cover rounded-lg" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5JTUc8L3RleHQ+PC9zdmc+'">
                    </td>
                `;
                tbody.appendChild(row);
                
                // Animate row entrance
                gsap.to(row, {
                    opacity: 1,
                    x: 0,
                    duration: 0.5,
                    delay: index * 0.05,
                    ease: "back.out(1.7)"
                });
            });
        }
        
        // Update quantity
        function updateQuantity(productId, change) {
            const product = products.find(p => p.id === productId);
            if (product) {
                product.quantity = Math.max(0, product.quantity + change);
                loadProducts();
                showMessage(`Updated ${product.name} (${product.size}) quantity to ${product.quantity}`, 'success');
            }
        }
        
        // Delete product
        function deleteProduct(productId) {
            if (confirm('Are you sure you want to delete this product?')) {
                const productIndex = products.findIndex(p => p.id === productId);
                if (productIndex > -1) {
                    const deletedProduct = products[productIndex];
                    products.splice(productIndex, 1);
                    loadProducts();
                    showMessage(`Deleted ${deletedProduct.name} (${deletedProduct.size})`, 'info');
                }
            }
        }
        
        // Add new product
        document.getElementById('addProductForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('productName').value;
            const size = document.getElementById('productSize').value;
            const quantity = parseInt(document.getElementById('productQuantity').value);
            const imageFile = document.getElementById('productImage').files[0];
            
            // Check if product with same name and size already exists
            const existingProduct = products.find(p => p.name === name && p.size === size);
            if (existingProduct) {
                showMessage('Product with this name and size already exists!', 'error');
                return;
            }
            
            let imagePath = 'images/default.jpg';
            
            // Handle image upload (placeholder implementation)
            if (imageFile) {
                // In a real implementation, you would upload the file to the server
                // For now, we'll create a placeholder path
                imagePath = `images/${imageFile.name}`;
                
                // Note: In a production environment, you would need server-side code
                // to handle file uploads. This is a client-side placeholder.
                showMessage('Note: Image upload requires server-side implementation for production use.', 'info');
            }
            
            const newProduct = {
                id: nextId++,
                name: name,
                size: size,
                quantity: quantity,
                image: imagePath
            };
            
            products.push(newProduct);
            loadProducts();
            
            // Reset form
            this.reset();
            
            showMessage(`Added ${name} (${size}) successfully!`, 'success');
        });
        
        // Show message
        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            
            const colors = {
                success: 'border-green-400 bg-green-900',
                error: 'border-red-400 bg-red-900',
                info: 'border-blue-400 bg-blue-900'
            };
            
            const icons = {
                success: 'fa-check-circle text-green-400',
                error: 'fa-exclamation-circle text-red-400',
                info: 'fa-info-circle text-blue-400'
            };
            
            messageDiv.className = `glassmorphism p-4 rounded-lg text-white max-w-sm ${colors[type]} transform translate-x-full opacity-0 transition-all duration-500`;
            messageDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${icons[type]} mr-3"></i>
                    <span>${message}</span>
                </div>
            `;
            
            container.appendChild(messageDiv);
            
            // Animate in
            setTimeout(() => {
                messageDiv.classList.remove('translate-x-full', 'opacity-0');
            }, 100);
            
            // Remove after 4 seconds
            setTimeout(() => {
                messageDiv.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (container.contains(messageDiv)) {
                        container.removeChild(messageDiv);
                    }
                }, 500);
            }, 4000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadProducts();
            
            // Animate header
            gsap.from("header", { duration: 1, y: -100, opacity: 0, ease: "back.out(1.7)" });
            gsap.from(".pulse-glow", { duration: 1, y: 50, opacity: 0, delay: 0.3, ease: "back.out(1.7)" });
        });
    </script>
</body>
</html>
