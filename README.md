# TrueGenZ - Premium GenZ Streetwear Sale Website

A production-grade, modern, aesthetic two-page sale website for TrueGenZ, a GenZ streetwear brand, featuring advanced animations, glassmorphism design, and real-time notifications.

## 🚀 Features

### Page 1: index.html (Public Sale Page)
- **Hero Section**: Animated gradient text with cycling brand colors
- **Product Grid**: Responsive grid with card flip animations and parallax hover effects
- **Real-time Notifications**: Fake purchase notifications with Indian names every 5-10 seconds
- **Dark Mode**: Smooth transitions for entire site
- **Advanced Animations**: GSAP-powered scroll reveals, staggered effects, and micro-interactions
- **Glassmorphism Design**: Premium translucent backgrounds and neon glow effects

### Page 2: admin.html (Stock Management Dashboard)
- **Clean Dashboard**: Professional dark-themed interface
- **Product Management**: Add, edit, and delete products with quantities
- **Image Upload**: Support for product image uploads (with placeholder implementation)
- **Animated Tables**: Sticky headers with smooth row hover animations
- **Real-time Updates**: Instant quantity adjustments with visual feedback

## 🎨 Design Features

### Brand Colors
- **Pink Accent**: #ff4181
- **Purple Accent**: #8e24aa

### Visual Effects
- Glassmorphism backgrounds with blur effects
- Neon glow hover animations
- Card flip interactions on product hover
- Parallax scrolling effects
- Gradient text animations
- Floating orb elements
- Pulse ring animations

### Typography
- **Font**: Google Fonts Poppins (300-900 weights)
- **Responsive**: Mobile-first design approach

## 📁 Project Structure

```
sale page/
├── index.html              # Main sale page
├── admin.html              # Admin dashboard
├── create_placeholders.html # Image placeholder generator
├── images/                 # Product images folder
│   ├── polo1.jpg
│   ├── hoodie1.jpg
│   ├── tee1.jpg
│   ├── jacket1.jpg
│   ├── shorts1.jpg
│   └── sweatshirt1.jpg
└── README.md              # This file
```

## 🛠️ Technologies Used

- **HTML5**: Semantic markup
- **Tailwind CSS**: Utility-first CSS framework
- **GSAP**: Advanced animations and scroll triggers
- **Google Fonts**: Poppins typography
- **Font Awesome**: Icon library
- **Vanilla JavaScript**: Core functionality

## 📦 CDN Dependencies

All dependencies are loaded via CDN for easy deployment:

- Tailwind CSS: `https://cdn.tailwindcss.com`
- GSAP Core: `https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js`
- GSAP ScrollTrigger: `https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js`
- GSAP ScrollTo: `https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollToPlugin.min.js`
- Font Awesome: `https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css`
- Google Fonts: Poppins family

## 🚀 Deployment Instructions

### For Hostinger Deployment:

1. **Upload Files**:
   - Upload `index.html`, `admin.html`, and the `images/` folder to your domain's public_html directory
   - Ensure the images folder contains all product images

2. **File Permissions**:
   - Set appropriate file permissions (644 for HTML files, 755 for directories)

3. **Image Setup**:
   - Use the `create_placeholders.html` file to generate sample product images
   - Save generated images to the `images/` folder
   - Or replace with actual product photos

4. **Domain Configuration**:
   - Point your domain to the uploaded files
   - Ensure index.html is set as the default page

### Local Development:

1. **Clone/Download** the project files
2. **Open** `create_placeholders.html` in a browser to generate sample images
3. **Save** generated images to the `images/` folder
4. **Open** `index.html` in a modern browser
5. **Access** admin panel via `admin.html`

## 🔧 Customization

### Adding New Products:
1. Use the admin panel (`admin.html`) to add products
2. Upload product images to the `images/` folder
3. Products are stored in browser localStorage

### Modifying Colors:
- Update CSS custom properties in the `<style>` sections
- Modify gradient definitions for brand consistency

### Animation Adjustments:
- GSAP timelines and triggers can be modified in the JavaScript sections
- Adjust duration, easing, and delay values as needed

## 📱 Responsive Design

- **Mobile-first**: Optimized for all screen sizes
- **Breakpoints**: Tailwind CSS responsive utilities
- **Touch-friendly**: Appropriate button sizes and spacing
- **Performance**: Optimized animations for mobile devices

## ⚡ Performance Features

- **Lazy Loading**: Images load as needed
- **Optimized Animations**: Hardware-accelerated CSS transforms
- **Minimal Dependencies**: CDN-based libraries for fast loading
- **Compressed Assets**: Optimized for production use

## 🔒 Security Notes

- **Client-side Storage**: Admin data stored in localStorage
- **Image Upload**: Placeholder implementation (requires server-side for production)
- **Input Validation**: Basic form validation included

## 🐛 Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile
- **Features**: CSS Grid, Flexbox, CSS Custom Properties, ES6+

## 📞 Support

For customization or deployment assistance, refer to the code comments and documentation within the HTML files.

---

**TrueGenZ** - Premium GenZ Streetwear Collection
*Built with modern web technologies for the next generation*
