<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrueGenZ - Mega Sale | Premium GenZ Streetwear</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts - Poppins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollToPlugin.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            font-family: 'Poppins', sans-serif;
        }
        
        .gradient-text {
            background: linear-gradient(45deg, #ff4181, #8e24aa, #ff4181);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .hero-bg {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            animation: heroGradient 8s ease-in-out infinite;
        }
        
        @keyframes heroGradient {
            0%, 100% { background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); }
            50% { background: linear-gradient(135deg, #0f3460 0%, #1a1a2e 50%, #16213e 100%); }
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .neon-glow {
            box-shadow: 0 0 20px rgba(255, 65, 129, 0.3);
            transition: all 0.3s ease;
        }
        
        .neon-glow:hover {
            box-shadow: 0 0 30px rgba(255, 65, 129, 0.6), 0 0 60px rgba(142, 36, 170, 0.4);
            transform: translateY(-2px) scale(1.02);
        }
        
        .product-card {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
        }
        
        .product-card:hover {
            transform: rotateY(5deg) rotateX(5deg) translateY(-10px);
        }
        
        .notification-popup {
            transform: translateX(400px);
            opacity: 0;
        }
        
        .notification-enter {
            animation: slideInNotification 0.5s ease-out forwards;
        }
        
        .notification-exit {
            animation: slideOutNotification 0.5s ease-in forwards;
        }
        
        @keyframes slideInNotification {
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOutNotification {
            to {
                transform: translateX(400px);
                opacity: 0;
            }
        }
        
        .dark .glassmorphism {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .neon-accent {
            border-color: rgba(255, 65, 129, 0.5);
            transition: all 0.3s ease;
        }

        .neon-accent:focus {
            border-color: #ff4181;
            box-shadow: 0 0 20px rgba(255, 65, 129, 0.4);
            outline: none;
        }

        .text-glow {
            text-shadow: 0 0 10px rgba(255, 65, 129, 0.5);
        }

        .bg-gradient-brand {
            background: linear-gradient(135deg, #ff4181 0%, #8e24aa 100%);
        }

        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .gradient-text {
                font-size: 3rem;
            }

            .floating-orb {
                display: none;
            }
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #ff4181, #8e24aa);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #8e24aa, #ff4181);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 65, 129, 0.4);
        }

        .floating-orb {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255, 65, 129, 0.3), rgba(142, 36, 170, 0.3));
            filter: blur(1px);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .card-flip {
            perspective: 1000px;
        }

        .card-flip-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }

        .card-flip:hover .card-flip-inner {
            transform: rotateY(180deg);
        }

        .card-flip-front, .card-flip-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 1rem;
        }

        .card-flip-back {
            transform: rotateY(180deg);
            background: linear-gradient(135deg, rgba(255, 65, 129, 0.9), rgba(142, 36, 170, 0.9));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .pulse-ring {
            position: absolute;
            border: 2px solid rgba(255, 65, 129, 0.5);
            border-radius: 50%;
            animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(2.5);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-500">
    <!-- Dark Mode Toggle -->
    <div class="fixed top-6 right-6 z-50">
        <button id="darkModeToggle" class="glassmorphism p-3 rounded-full text-gray-800 dark:text-white hover:scale-110 transition-all duration-300">
            <i class="fas fa-moon dark:hidden"></i>
            <i class="fas fa-sun hidden dark:inline"></i>
        </button>
    </div>
    
    <!-- Notification Container -->
    <div id="notificationContainer" class="fixed top-6 right-20 z-40 space-y-3"></div>
    
    <!-- Hero Section -->
    <section class="hero-bg min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="container mx-auto px-6 text-center relative z-10">
            <h1 class="text-6xl md:text-8xl font-black mb-6 gradient-text" id="heroTitle">
                TrueGenZ
            </h1>
            <h2 class="text-3xl md:text-5xl font-bold text-white mb-8" id="saleText">
                MEGA SALE - UP TO 50% OFF
            </h2>
            <p class="text-xl text-gray-300 mb-12 max-w-2xl mx-auto" id="heroSubtext">
                Premium GenZ Streetwear Collection. Limited Time Offer.
            </p>
            <button class="btn-primary text-white px-12 py-4 rounded-full text-lg font-semibold neon-glow" id="shopNowBtn">
                Shop Now <i class="fas fa-arrow-right ml-2"></i>
            </button>
        </div>
        
        <!-- Floating Elements -->
        <div class="floating-orb w-20 h-20 top-20 left-10" style="animation-delay: 0s;"></div>
        <div class="floating-orb w-16 h-16 bottom-20 right-10" style="animation-delay: 1s;"></div>
        <div class="floating-orb w-12 h-12 top-1/2 left-20" style="animation-delay: 2s;"></div>
        <div class="floating-orb w-24 h-24 top-32 right-32" style="animation-delay: 3s;"></div>
        <div class="floating-orb w-14 h-14 bottom-32 left-32" style="animation-delay: 4s;"></div>
        <div class="floating-orb w-18 h-18 top-3/4 right-20" style="animation-delay: 5s;"></div>
    </section>
    
    <!-- Products Section -->
    <section class="py-20 bg-white dark:bg-gray-800 transition-colors duration-500">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-16 text-gray-800 dark:text-white">
                Featured Products
            </h2>
            
            <div id="productsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                <!-- Products will be dynamically loaded here -->
            </div>
        </div>
    </section>
    
    <!-- Admin Link -->
    <div class="fixed bottom-6 left-6 z-50">
        <a href="admin.html" class="glassmorphism p-3 rounded-full text-gray-800 dark:text-white hover:scale-110 transition-all duration-300">
            <i class="fas fa-cog"></i>
        </a>
    </div>

    <script>
        // Sample products data
        const products = [
            { id: 1, name: "Need For Speed Polo", image: "images/polo1.jpg", sizes: ["S", "M", "L", "XL"], price: "₹1,999", originalPrice: "₹3,999" },
            { id: 2, name: "Urban Rebel Hoodie", image: "images/hoodie1.jpg", sizes: ["S", "M", "L", "XL"], price: "₹2,499", originalPrice: "₹4,999" },
            { id: 3, name: "Street King Tee", image: "images/tee1.jpg", sizes: ["S", "M", "L", "XL"], price: "₹899", originalPrice: "₹1,799" },
            { id: 4, name: "Neon Dreams Jacket", image: "images/jacket1.jpg", sizes: ["S", "M", "L", "XL"], price: "₹3,499", originalPrice: "₹6,999" },
            { id: 5, name: "Cyber Punk Shorts", image: "images/shorts1.jpg", sizes: ["S", "M", "L", "XL"], price: "₹1,299", originalPrice: "₹2,599" },
            { id: 6, name: "Galaxy Vibes Sweatshirt", image: "images/sweatshirt1.jpg", sizes: ["S", "M", "L", "XL"], price: "₹2,199", originalPrice: "₹4,399" }
        ];
        
        // Indian names for notifications
        const indianNames = ["Aarav", "Saanvi", "Krish", "Anaya", "Vihaan", "Aanya", "Arjun", "Isha", "Reyansh", "Kavya", "Ayaan", "Diya", "Vivaan", "Priya", "Aditya", "Riya"];
        
        // Dark mode functionality
        const darkModeToggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;
        
        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            html.classList.add('dark');
        }
        
        darkModeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.setItem('darkMode', html.classList.contains('dark'));
        });
        
        // Load products
        function loadProducts() {
            const grid = document.getElementById('productsGrid');
            grid.innerHTML = '';
            
            products.forEach((product, index) => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card card-flip glassmorphism rounded-2xl neon-glow opacity-0 relative';
                productCard.innerHTML = `
                    <div class="card-flip-inner h-full">
                        <div class="card-flip-front glassmorphism rounded-2xl p-6 h-full">
                            <div class="relative mb-4 overflow-hidden rounded-xl">
                                <img src="${product.image}" alt="${product.name}" class="w-full h-64 object-cover transition-transform duration-500 hover:scale-110" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPiR7cHJvZHVjdC5uYW1lfTwvdGV4dD48L3N2Zz4='">
                                <div class="absolute top-3 right-3 bg-red-500 text-white px-2 py-1 rounded-full text-sm font-semibold pulse-ring">
                                    50% OFF
                                </div>
                            </div>
                            <h3 class="text-xl font-bold mb-3 text-gray-800 dark:text-white">${product.name}</h3>
                            <div class="flex items-center mb-4">
                                <span class="text-2xl font-bold text-pink-500">${product.price}</span>
                                <span class="text-lg text-gray-500 line-through ml-2">${product.originalPrice}</span>
                            </div>
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Size:</label>
                                <select class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white neon-accent">
                                    ${product.sizes.map(size => `<option value="${size}">${size}</option>`).join('')}
                                </select>
                            </div>
                            <button class="btn-primary w-full text-white py-3 rounded-lg font-semibold hover:scale-105 transition-all duration-300" onclick="buyProduct('${product.name}')">
                                Add to Cart <i class="fas fa-shopping-cart ml-2"></i>
                            </button>
                        </div>
                        <div class="card-flip-back">
                            <div class="text-center p-6">
                                <i class="fas fa-star text-4xl mb-4"></i>
                                <h3 class="text-2xl font-bold mb-4">${product.name}</h3>
                                <p class="text-lg mb-4">Premium GenZ Streetwear</p>
                                <div class="flex justify-center space-x-2 mb-4">
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <i class="fas fa-star text-yellow-400"></i>
                                </div>
                                <p class="text-sm opacity-90">Limited Edition Collection</p>
                            </div>
                        </div>
                    </div>
                `;
                grid.appendChild(productCard);
                
                // Animate card entrance
                gsap.to(productCard, {
                    opacity: 1,
                    y: 0,
                    duration: 0.6,
                    delay: index * 0.1,
                    ease: "back.out(1.7)"
                });
            });
        }
        
        // Buy product function
        function buyProduct(productName) {
            // Add to cart logic here
            showNotification(`Added ${productName} to cart!`, 'success');
        }
        
        // Show notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `notification-popup glassmorphism p-4 rounded-lg text-white max-w-sm ${type === 'success' ? 'border-green-400' : 'border-blue-400'}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle text-green-400' : 'fa-info-circle text-blue-400'} mr-3"></i>
                    <span>${message}</span>
                </div>
            `;
            
            container.appendChild(notification);
            
            // Animate in
            setTimeout(() => notification.classList.add('notification-enter'), 100);
            
            // Remove after 4 seconds
            setTimeout(() => {
                notification.classList.add('notification-exit');
                setTimeout(() => container.removeChild(notification), 500);
            }, 4000);
        }
        
        // Random purchase notifications
        function showRandomPurchaseNotification() {
            const randomName = indianNames[Math.floor(Math.random() * indianNames.length)];
            const randomProduct = products[Math.floor(Math.random() * products.length)];
            const message = `${randomName} just bought ${randomProduct.name}!`;
            
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = 'notification-popup glassmorphism p-4 rounded-lg text-white max-w-sm border-pink-400';
            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-shopping-bag text-white"></i>
                    </div>
                    <div>
                        <div class="font-semibold">${randomName}</div>
                        <div class="text-sm opacity-90">just bought ${randomProduct.name}!</div>
                    </div>
                </div>
            `;
            
            container.appendChild(notification);
            
            // Animate in
            setTimeout(() => notification.classList.add('notification-enter'), 100);
            
            // Remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('notification-exit');
                setTimeout(() => {
                    if (container.contains(notification)) {
                        container.removeChild(notification);
                    }
                }, 500);
            }, 5000);
        }
        
        // Parallax effect for hero section
        function initParallax() {
            gsap.to(".hero-bg", {
                yPercent: -50,
                ease: "none",
                scrollTrigger: {
                    trigger: ".hero-bg",
                    start: "top bottom",
                    end: "bottom top",
                    scrub: true
                }
            });

            // Floating elements parallax
            gsap.to(".hero-bg .absolute", {
                y: -100,
                rotation: 360,
                ease: "none",
                scrollTrigger: {
                    trigger: ".hero-bg",
                    start: "top bottom",
                    end: "bottom top",
                    scrub: 1
                }
            });
        }

        // Advanced scroll reveal animations
        function initScrollAnimations() {
            // Products section reveal
            gsap.from("#productsGrid", {
                y: 100,
                opacity: 0,
                duration: 1,
                scrollTrigger: {
                    trigger: "#productsGrid",
                    start: "top 80%",
                    end: "bottom 20%",
                    toggleActions: "play none none reverse"
                }
            });

            // Staggered product card animations
            gsap.from(".product-card", {
                y: 80,
                opacity: 0,
                duration: 0.8,
                stagger: 0.1,
                ease: "back.out(1.7)",
                scrollTrigger: {
                    trigger: "#productsGrid",
                    start: "top 70%",
                    toggleActions: "play none none reverse"
                }
            });
        }

        // Enhanced product card interactions
        function enhanceProductCards() {
            document.querySelectorAll('.product-card').forEach(card => {
                card.addEventListener('mouseenter', () => {
                    gsap.to(card, {
                        scale: 1.05,
                        rotationY: 5,
                        rotationX: 5,
                        duration: 0.3,
                        ease: "power2.out"
                    });

                    gsap.to(card.querySelector('img'), {
                        scale: 1.1,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });

                card.addEventListener('mouseleave', () => {
                    gsap.to(card, {
                        scale: 1,
                        rotationY: 0,
                        rotationX: 0,
                        duration: 0.3,
                        ease: "power2.out"
                    });

                    gsap.to(card.querySelector('img'), {
                        scale: 1,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Load products
            loadProducts();

            // Start random notifications
            setInterval(showRandomPurchaseNotification, Math.random() * 5000 + 5000); // 5-10 seconds

            // GSAP animations
            gsap.registerPlugin(ScrollTrigger);

            // Hero animations with enhanced effects
            const tl = gsap.timeline();
            tl.from("#heroTitle", {
                duration: 1.5,
                y: 100,
                opacity: 0,
                ease: "back.out(1.7)",
                onComplete: () => {
                    gsap.to("#heroTitle", {
                        textShadow: "0 0 20px rgba(255, 65, 129, 0.5)",
                        duration: 2,
                        repeat: -1,
                        yoyo: true
                    });
                }
            })
            .from("#saleText", { duration: 1.5, y: 50, opacity: 0, ease: "back.out(1.7)" }, "-=1.2")
            .from("#heroSubtext", { duration: 1.5, y: 30, opacity: 0, ease: "back.out(1.7)" }, "-=1")
            .from("#shopNowBtn", { duration: 1.5, y: 30, opacity: 0, ease: "back.out(1.7)" }, "-=0.8");

            // Initialize advanced animations
            initParallax();
            initScrollAnimations();

            // Enhance product cards after they're loaded
            setTimeout(enhanceProductCards, 1000);

            // Scroll to products when shop now is clicked
            document.getElementById('shopNowBtn').addEventListener('click', () => {
                gsap.to(window, {
                    duration: 1.5,
                    scrollTo: "#productsGrid",
                    ease: "power2.inOut"
                });
            });

            // Add scroll-triggered text animations
            gsap.from("h2", {
                y: 50,
                opacity: 0,
                duration: 1,
                stagger: 0.2,
                scrollTrigger: {
                    trigger: "h2",
                    start: "top 80%",
                    toggleActions: "play none none reverse"
                }
            });
        });
    </script>
</body>
</html>
